"use client";

import React, { useState, useMemo, useCallback } from "react";
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  useTheme
} from "@mui/material";
import { PageHeader, SearchBar, TableComponent, ActiveColumn } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";
import { Icon } from "@/components/common/icon";
import {
  faEdit,
  faTrash,
  faPlus,
  faUser
} from "@fortawesome/free-solid-svg-icons";
import { styled } from "@mui/material/styles";

// Styled components
const TableContainer = styled(Box)(({ theme }) => ({
  overflowX: "auto",
  width: "100%",
  "& table": {
    width: "100%",
    borderCollapse: "separate",
    borderSpacing: 0,
  },
  "& th": {
    padding: theme.spacing(1.5),
    fontWeight: 600,
    textAlign: "left",
    backgroundColor: theme.palette.mode === "dark" ? theme.palette.grey[800] : theme.palette.grey[100],
    color: theme.palette.text.primary,
    position: "sticky",
    top: 0,
    zIndex: 1,
    borderBottom: `1px solid ${theme.palette.divider}`,
    "&:first-of-type": {
      borderTopLeftRadius: theme.shape.borderRadius,
    },
    "&:last-of-type": {
      borderTopRightRadius: theme.shape.borderRadius,
    },
  },
  "& td": {
    padding: theme.spacing(1.5),
    borderBottom: `1px solid ${theme.palette.divider}`,
    color: theme.palette.text.secondary,
  },
  "& tr:last-of-type td": {
    borderBottom: "none",
  },
  "& tr:hover td": {
    backgroundColor: theme.palette.action.hover,
  },
}));

const PaginationContainer = styled(Box)(({ theme }) => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: theme.spacing(2),
  borderTop: `1px solid ${theme.palette.divider}`,
}));

const PageButton = styled(Button)(({ theme }) => ({
  minWidth: "36px",
  padding: theme.spacing(0.5, 1),
  margin: theme.spacing(0, 0.5),
}));

// Mock data for customers
const mockCustomers = [
  {
    id: 1,
    sn: 1,
    logo: "O",
    name: "Oracle",
    email: "<EMAIL>",
    contact: "7418523647",
    createdAt: "19-05-2023 08:18 PM",
  },
  {
    id: 2,
    sn: 2,
    logo: "L",
    name: "LinkedIn",
    email: "<EMAIL>",
    contact: "7418523646",
    createdAt: "19-05-2023 08:17 PM",
  },
  {
    id: 3,
    sn: 3,
    logo: "T",
    name: "Twitter",
    email: "<EMAIL>",
    contact: "7418523645",
    createdAt: "19-05-2023 08:15 PM",
  },
  {
    id: 4,
    sn: 4,
    logo: "T",
    name: "Tesla",
    email: "<EMAIL>",
    contact: "7418523699",
    createdAt: "19-05-2023 08:14 PM",
  },
  {
    id: 5,
    sn: 5,
    logo: "I",
    name: "IBM",
    email: "<EMAIL>",
    contact: "7418523698",
    createdAt: "19-05-2023 08:12 PM",
  },
  {
    id: 6,
    sn: 6,
    logo: "M",
    name: "Microsoft",
    email: "<EMAIL>",
    contact: "7418523697",
    createdAt: "19-05-2023 08:10 PM",
  },
  {
    id: 7,
    sn: 7,
    logo: "A",
    name: "Apple",
    email: "<EMAIL>",
    contact: "7418523696",
    createdAt: "19-05-2023 08:08 PM",
  },
  {
    id: 8,
    sn: 8,
    logo: "G",
    name: "Google",
    email: "<EMAIL>",
    contact: "7418523695",
    createdAt: "19-05-2023 08:05 PM",
  },
  {
    id: 9,
    sn: 9,
    logo: "F",
    name: "Facebook",
    email: "<EMAIL>",
    contact: "7418523694",
    createdAt: "19-05-2023 08:03 PM",
  },
  {
    id: 10,
    sn: 10,
    logo: "A",
    name: "Amazon",
    email: "<EMAIL>",
    contact: "7418523693",
    createdAt: "19-05-2023 08:01 PM",
  },
  {
    id: 11,
    sn: 11,
    logo: "N",
    name: "Netflix",
    email: "<EMAIL>",
    contact: "7418523692",
    createdAt: "19-05-2023 07:58 PM",
  },
  {
    id: 12,
    sn: 12,
    logo: "S",
    name: "Salesforce",
    email: "<EMAIL>",
    contact: "7418523691",
    createdAt: "19-05-2023 07:55 PM",
  },
  {
    id: 13,
    sn: 13,
    logo: "A",
    name: "Adobe",
    email: "<EMAIL>",
    contact: "7418523690",
    createdAt: "19-05-2023 07:52 PM",
  },
  {
    id: 14,
    sn: 14,
    logo: "I",
    name: "Intel",
    email: "<EMAIL>",
    contact: "7418523689",
    createdAt: "19-05-2023 07:50 PM",
  },
];

const CustomersPage = () => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentCustomer, setCurrentCustomer] = useState<any>(null);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "sn",
    "logo",
    "name",
    "email",
    "contact",
    "createdAt",
    "actions",
  ]);

  // Column options for the ActiveColumn component
  const columnOptions = [
    { value: "sn", label: "SN" },
    { value: "logo", label: "Logo" },
    { value: "name", label: "Name" },
    { value: "email", label: "Email" },
    { value: "contact", label: "Contact" },
    { value: "createdAt", label: "Created At" },
    { value: "actions", label: "Actions" },
  ];

  // Filter options for the SearchBar component
  const filterOptions = [
    { value: "name", label: "Name" },
    { value: "email", label: "Email" },
    { value: "contact", label: "Contact" },
  ];

  // Handle search
  const handleSearch = useCallback((value: string, filters: string[] = []) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page on search
  }, []);

  // Handle column visibility change
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  // Handle edit
  const handleEdit = useCallback((customer: any) => {
    setCurrentCustomer(customer);
    setEditDialogOpen(true);
  }, []);

  // Handle delete
  const handleDelete = useCallback((customer: any) => {
    setCurrentCustomer(customer);
    setDeleteDialogOpen(true);
  }, []);

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Handle rows per page change
  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1); // Reset to first page when changing rows per page
  }, []);

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm) return mockCustomers;

    const searchLower = searchTerm.toLowerCase();
    return mockCustomers.filter(customer =>
      customer.name.toLowerCase().includes(searchLower) ||
      customer.email.toLowerCase().includes(searchLower) ||
      customer.contact.includes(searchLower)
    );
  }, [mockCustomers, searchTerm]);

  // Calculate total pages
  const totalCount = filteredData.length;

  // Create table headers
  const headers = useMemo(() => [
    { id: "sn", label: "SN", sortable: true },
    { id: "logo", label: "LOGO", sortable: false,
      renderCell: (row: any) => (
        <Box
          sx={{
            width: 30,
            height: 30,
            borderRadius: '50%',
            backgroundColor: theme.palette.primary.main,
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
          }}
        >
          {row.logo}
        </Box>
      )
    },
    { id: "name", label: "NAME", sortable: true },
    { id: "email", label: "EMAIL", sortable: true },
    { id: "contact", label: "CONTACT", sortable: true },
    { id: "createdAt", label: "CREATED AT", sortable: true },
    {
      id: "actions",
      label: "ACTIONS",
      sortable: false,
      renderCell: (row: any) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={() => handleEdit(row)}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            Edit
          </Button>
          <Button
            variant="contained"
            color="error"
            size="small"
            onClick={() => handleDelete(row)}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            Delete
          </Button>
        </Box>
      )
    },
  ], [theme.palette.primary.main, handleEdit, handleDelete]);

  // Create header with search and column selector
  const header = useMemo(() => (
    <SuppressHydrationWarning>
      <PageHeader
        title="Customer Management"
        breadcrumbs={[
          { name: "Dashboard", path: "/dashboard", forwardParam: false },
          { name: "Super Admin", path: "/super-admin/dashboard", forwardParam: false },
          { name: "Customers", path: "/super-admin/customers", forwardParam: false },
        ]}
        actions={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SearchBar
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              onClear={() => handleSearch("")}
              sx={{ minWidth: 300 }}
            />
            <ActiveColumn
              columnOptions={columnOptions}
              visibleColumns={visibleColumns}
              onColumnVisibilityChange={handleColumnVisibilityChange}
              variant="popover"
            />
            <Button
              variant="contained"
              startIcon={<Icon icon={faPlus} size="small" onlyIcon />}
              onClick={() => {
                setCurrentCustomer(null);
                setEditDialogOpen(true);
              }}
              size="small"
              sx={{
                bgcolor: '#3A52A6',
                '&:hover': {
                  bgcolor: '#2A3F8F',
                },
                padding: '6px 16px',
                fontWeight: 500,
              }}
            >
              Add Customer
            </Button>
          </Box>
        }
      />
    </SuppressHydrationWarning>
  ), [handleSearch, filterOptions, columnOptions, visibleColumns, handleColumnVisibilityChange]);

  // Create table component
  const table = useMemo(() => (
    <Paper elevation={0} sx={{ mt: 3, borderRadius: 1, overflow: 'hidden' }}>
      <SuppressHydrationWarning>
        <TableComponent<any>
          isLoading={false}
          headerField={headers}
          tableBody={filteredData}
          paginationData={{
            onPageChange: handlePageChange,
            onRowsPerPageChange: handleRowsPerPageChange,
            total: totalCount,
            page: page,
            limit: rowsPerPage,
          }}
          translation={{
            noDataTitle: "No Customers Found",
          }}
          maxHeight={600}
          onRowClick={() => {}}
          visibleColumns={visibleColumns}
        />
      </SuppressHydrationWarning>
    </Paper>
  ), [
    headers,
    filteredData,
    handlePageChange,
    handleRowsPerPageChange,
    totalCount,
    page,
    rowsPerPage,
    visibleColumns,
  ]);

  return (
    <div>
      {header}
      {table}

      {/* Edit Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>{currentCustomer ? "Edit Customer" : "Add New Customer"}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Customer Name"
                fullWidth
                defaultValue={currentCustomer?.name || ""}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Email"
                fullWidth
                defaultValue={currentCustomer?.email || ""}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Contact"
                fullWidth
                defaultValue={currentCustomer?.contact || ""}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Logo Initial"
                fullWidth
                defaultValue={currentCustomer?.logo || ""}
                inputProps={{ maxLength: 1 }}
                helperText="Enter a single character for the logo"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={() => {
              // In a real app, you would save the changes here
              setEditDialogOpen(false);
            }}
          >
            {currentCustomer ? "Save Changes" : "Add Customer"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete customer "{currentCustomer?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              // In a real app, you would delete the customer here
              setDeleteDialogOpen(false);
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default CustomersPage;
